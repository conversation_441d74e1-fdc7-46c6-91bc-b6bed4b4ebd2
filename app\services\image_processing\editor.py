"""
Модуль для редактирования изображений через внешние API.

Предоставляет функции для редактирования изображений с использованием
различных AI-сервисов, включая Pollinations API с моделью kontext.
"""

import logging
import aiohttp
import asyncio
import random
import urllib.parse
from typing import Optional, Dict, Any
from io import BytesIO

logger = logging.getLogger(__name__)


class ImageEditingError(Exception):
    """Исключение для ошибок редактирования изображений."""
    pass


async def edit_image_with_pollinations(image_url: str, 
                                     prompt: str, 
                                     model: str = "kontext",
                                     max_retries: int = 3) -> Optional[bytes]:
    """
    Редактирует изображение через Pollinations API с использованием модели kontext.
    
    Args:
        image_url: Публичная ссылка на исходное изображение
        prompt: Текстовое описание желаемых изменений
        model: Модель для редактирования (по умолчанию "kontext")
        max_retries: Максимальное количество попыток
        
    Returns:
        Байты отредактированного изображения или None в случае ошибки
        
    Raises:
        ImageEditingError: При критических ошибках редактирования
    """
    try:
        # Очищаем и подготавливаем промпт
        cleaned_prompt = _sanitize_prompt(prompt)
        if not cleaned_prompt:
            raise ImageEditingError("Пустой или некорректный промпт для редактирования")
        
        # Формируем параметры запроса
        params = {
            "model": model,
            "prompt": cleaned_prompt,
            "image": image_url,
            "seed": random.randint(1, 999999),
            "nologo": "true",
            "private": "true",
            "safe": "false"
        }
        
        # Добавляем токен если доступен
        from config.image_generation_config import IMAGE_MODELS_CONFIG
        kontext_config = IMAGE_MODELS_CONFIG.get("kontext", {}).get("params", {})
        
        # Получаем токен из переменных окружения
        import os
        api_token = os.getenv(kontext_config.get("api_token_env", ""))
        if api_token:
            params["token"] = api_token
        
        # Формируем URL
        base_url = "https://image.pollinations.ai/prompt"
        encoded_prompt = urllib.parse.quote(cleaned_prompt)
        url = f"{base_url}/{encoded_prompt}"
        
        # Настройки запроса
        timeout = aiohttp.ClientTimeout(total=60)  # Увеличенный таймаут для редактирования
        headers = {}
        
        # Добавляем реферер если доступен
        referrer = os.getenv(kontext_config.get("referrer_env", ""))
        if referrer:
            headers["Referer"] = referrer
        
        # Выполняем запрос с повторными попытками
        for attempt in range(max_retries):
            try:
                logger.info(f"Редактирование изображения через Pollinations, попытка {attempt + 1}")
                logger.debug(f"URL: {url}")
                logger.debug(f"Параметры: {params}")
                
                async with aiohttp.ClientSession(timeout=timeout) as session:
                    async with session.get(url, params=params, headers=headers) as response:
                        if response.status == 200:
                            image_bytes = await response.read()
                            
                            # Проверяем что получили валидное изображение
                            if len(image_bytes) > 1000:  # Минимальный размер изображения
                                logger.info("Изображение успешно отредактировано через Pollinations")
                                return image_bytes
                            else:
                                logger.warning("Получено слишком маленькое изображение")
                        
                        elif response.status == 429:  # Rate limit
                            wait_time = 2 ** attempt  # Экспоненциальная задержка
                            logger.warning(f"Rate limit, ожидание {wait_time} секунд")
                            await asyncio.sleep(wait_time)
                            continue
                        
                        else:
                            logger.warning(f"Ошибка API Pollinations: HTTP {response.status}")
                
                # Задержка между попытками
                if attempt < max_retries - 1:
                    await asyncio.sleep(2)
                    
            except asyncio.TimeoutError:
                logger.warning(f"Таймаут при редактировании, попытка {attempt + 1}")
                if attempt < max_retries - 1:
                    await asyncio.sleep(3)
            except aiohttp.ClientError as e:
                logger.warning(f"Сетевая ошибка при редактировании, попытка {attempt + 1}: {e}")
                if attempt < max_retries - 1:
                    await asyncio.sleep(2)
        
        logger.error("Все попытки редактирования через Pollinations неудачны")
        return None
        
    except Exception as e:
        logger.exception(f"Критическая ошибка при редактировании изображения: {e}")
        raise ImageEditingError(f"Ошибка редактирования: {e}") from e


def _sanitize_prompt(prompt: str) -> str:
    """
    Очищает и подготавливает промпт для API.
    
    Args:
        prompt: Исходный промпт
        
    Returns:
        Очищенный промпт
    """
    if not prompt:
        return ""
    
    # Убираем лишние пробелы
    cleaned = " ".join(prompt.split())
    
    # Ограничиваем длину (для Pollinations API)
    max_length = 200
    if len(cleaned) > max_length:
        cleaned = cleaned[:max_length].rsplit(" ", 1)[0]
    
    return cleaned


async def validate_edit_result(image_bytes: bytes) -> bool:
    """
    Проверяет валидность результата редактирования.
    
    Args:
        image_bytes: Байты изображения для проверки
        
    Returns:
        True если изображение валидно, False иначе
    """
    try:
        # Базовые проверки
        if not image_bytes or len(image_bytes) < 1000:
            return False
        
        # Проверяем заголовки популярных форматов изображений
        png_header = b'\x89PNG\r\n\x1a\n'
        jpeg_header = b'\xff\xd8\xff'
        webp_header = b'RIFF'
        
        if (image_bytes.startswith(png_header) or 
            image_bytes.startswith(jpeg_header) or 
            image_bytes.startswith(webp_header)):
            return True
        
        return False
        
    except Exception as e:
        logger.warning(f"Ошибка валидации изображения: {e}")
        return False


async def get_editing_capabilities() -> Dict[str, Any]:
    """
    Возвращает информацию о возможностях редактирования.
    
    Returns:
        Словарь с описанием доступных операций редактирования
    """
    return {
        "supported_models": ["kontext"],
        "max_prompt_length": 200,
        "supported_formats": ["PNG", "JPEG", "WebP"],
        "max_retries": 3,
        "timeout_seconds": 60,
        "features": [
            "Изменение объектов на изображении",
            "Добавление новых элементов",
            "Изменение стиля и цветов",
            "Модификация фона",
            "Художественные эффекты"
        ]
    }
