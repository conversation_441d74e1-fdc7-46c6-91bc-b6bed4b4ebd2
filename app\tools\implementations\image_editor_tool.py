"""
Инструмент для редактирования изображений через Tool Calling API.

Этот инструмент позволяет редактировать изображения, которые были загружены пользователем
или сгенерированы ботом, используя текстовые команды на естественном языке.
"""

import json
import logging
from typing import Optional
from aiogram import Bot
from aiogram.types import BufferedInputFile
from aiogram.fsm.context import FSMContext

from app.services.image_processing.uploader import upload_to_public_host, ImageUploadError
from app.services.image_processing.editor import edit_image_with_pollinations, ImageEditingError
from app.states.image_editing import ImageContext, ImageEditing

logger = logging.getLogger(__name__)


class ImageEditorTool:
    """
    Инструмент для редактирования изображений.
    
    Следует протоколу Tool из app.tools.base_tool и интегрируется
    с системой FSM для отслеживания контекста изображений.
    """
    
    name: str = "edit_image"
    description: str = (
        "Edits the last sent or generated image based on user's text instructions. "
        "Use this tool when a user asks to change, modify, adjust, or alter an existing image. "
        "The image must have been previously uploaded by the user or generated by the bot."
    )
    schema: dict = {
        "type": "object",
        "properties": {
            "prompt": {
                "type": "string",
                "description": (
                    "A clear, concise instruction in English on how to edit the image. "
                    "Examples: 'make his eyes blue', 'add a pirate hat', 'change background to forest', "
                    "'make it more colorful', 'remove the car from the image'."
                )
            }
        },
        "required": ["prompt"]
    }

    async def execute(self, bot: Bot, chat_id: int, state: FSMContext, **kwargs) -> str:
        """
        Основная логика редактирования изображения.
        
        Args:
            bot: Экземпляр aiogram Bot
            chat_id: ID чата для отправки результата
            state: FSMContext для работы с состоянием
            **kwargs: Параметры инструмента (prompt)
            
        Returns:
            JSON строка с результатом выполнения
        """
        prompt = kwargs.get("prompt")
        if not prompt:
            return json.dumps({
                "error": "Prompt for editing is missing.",
                "user_message": "Не указана команда для редактирования изображения."
            })

        try:
            # 1. Проверяем наличие изображения в контексте
            current_image_id = await ImageContext.get_current_image(state)
            
            if not current_image_id:
                return json.dumps({
                    "error": "No image found in context for editing.",
                    "user_message": (
                        "Для редактирования нужно сначала загрузить изображение. "
                        "Пожалуйста, отправьте фотографию, которую хотите отредактировать."
                    )
                })

            # 2. Отправляем сообщение о начале обработки
            processing_message = await bot.send_message(
                chat_id=chat_id,
                text="🎨 Редактирую изображение... Это может занять несколько секунд."
            )

            # 3. Скачиваем изображение из Telegram
            try:
                file_info = await bot.get_file(current_image_id)
                image_bytes = await bot.download_file(file_info.file_path)
                image_data = image_bytes.read()
                
                logger.info(f"Скачано изображение для редактирования: {len(image_data)} байт")
                
            except Exception as e:
                logger.error(f"Ошибка скачивания изображения из Telegram: {e}")
                await bot.edit_message_text(
                    text="❌ Ошибка при скачивании изображения.",
                    chat_id=chat_id,
                    message_id=processing_message.message_id
                )
                return json.dumps({
                    "error": f"Failed to download image from Telegram: {e}",
                    "user_message": "Не удалось скачать изображение для редактирования."
                })

            # 4. Загружаем изображение на временный хостинг
            try:
                temp_image_url = await upload_to_public_host(image_data)
                
                if not temp_image_url:
                    raise ImageUploadError("Не удалось загрузить изображение на временный хостинг")
                
                logger.info(f"Изображение загружено на временный хостинг: {temp_image_url}")
                
            except ImageUploadError as e:
                logger.error(f"Ошибка загрузки на хостинг: {e}")
                await bot.edit_message_text(
                    text="❌ Ошибка при загрузке изображения на временный хостинг.",
                    chat_id=chat_id,
                    message_id=processing_message.message_id
                )
                return json.dumps({
                    "error": f"Image upload failed: {e}",
                    "user_message": "Не удалось подготовить изображение для редактирования."
                })

            # 5. Выполняем редактирование через Pollinations API
            try:
                edited_image_bytes = await edit_image_with_pollinations(temp_image_url, prompt)
                
                if not edited_image_bytes:
                    raise ImageEditingError("API редактирования вернул пустой результат")
                
                logger.info(f"Изображение успешно отредактировано: {len(edited_image_bytes)} байт")
                
            except ImageEditingError as e:
                logger.error(f"Ошибка редактирования: {e}")
                await bot.edit_message_text(
                    text="❌ Ошибка при редактировании изображения. Попробуйте другую команду.",
                    chat_id=chat_id,
                    message_id=processing_message.message_id
                )
                return json.dumps({
                    "error": f"Image editing failed: {e}",
                    "user_message": "Не удалось отредактировать изображение. Попробуйте изменить команду."
                })

            # 6. Удаляем сообщение о процессе
            try:
                await bot.delete_message(chat_id=chat_id, message_id=processing_message.message_id)
            except Exception:
                pass  # Игнорируем ошибки удаления сообщения

            # 7. Отправляем результат пользователю
            try:
                photo_to_send = BufferedInputFile(edited_image_bytes, filename="edited_image.png")
                
                # Формируем подпись
                edit_info = await ImageContext.get_edit_info(state)
                edit_count = edit_info.get('edit_count', 0) + 1
                
                caption = f"✅ Изображение отредактировано: '{prompt}'\n📝 Правка #{edit_count}"
                
                sent_message = await bot.send_photo(
                    chat_id=chat_id,
                    photo=photo_to_send,
                    caption=caption
                )
                
                # 8. Обновляем контекст с новым изображением
                new_file_id = sent_message.photo[-1].file_id
                await ImageContext.update_after_edit(state, new_file_id, prompt)
                
                # Убеждаемся что состояние установлено для продолжения редактирования
                await state.set_state(ImageEditing.waiting_for_prompt)
                
                logger.info(f"Редактирование завершено успешно, новый file_id: {new_file_id}")
                
                return json.dumps({
                    "success": True,
                    "message": "Image successfully edited and sent to user",
                    "edit_count": edit_count,
                    "prompt_used": prompt
                })
                
            except Exception as e:
                logger.error(f"Ошибка отправки отредактированного изображения: {e}")
                return json.dumps({
                    "error": f"Failed to send edited image: {e}",
                    "user_message": "Изображение отредактировано, но не удалось его отправить."
                })

        except Exception as e:
            logger.exception(f"Неожиданная ошибка в ImageEditorTool: {e}")
            
            # Пытаемся отправить сообщение об ошибке пользователю
            try:
                await bot.send_message(
                    chat_id=chat_id,
                    text="❌ Произошла неожиданная ошибка при редактировании изображения."
                )
            except Exception:
                pass
            
            return json.dumps({
                "error": f"Unexpected error in image editing: {e}",
                "user_message": "Произошла неожиданная ошибка. Попробуйте еще раз."
            })
