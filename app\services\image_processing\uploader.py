"""
Модуль для загрузки изображений на временный хостинг.

Предоставляет функции для загрузки изображений на публичные временные хостинги,
такие как uguu.se, для последующего использования в API редактирования изображений.
"""

import logging
import aiohttp
import asyncio
from typing import Optional, Union
from io import BytesIO

logger = logging.getLogger(__name__)


class ImageUploadError(Exception):
    """Исключение для ошибок загрузки изображений."""
    pass


async def upload_to_uguu(image_data: Union[bytes, BytesIO]) -> Optional[str]:
    """
    Загружает изображение на uguu.se и возвращает публичную ссылку.
    
    Args:
        image_data: Данные изображения в виде bytes или BytesIO
        
    Returns:
        Публичная ссылка на загруженное изображение или None в случае ошибки
        
    Raises:
        ImageUploadError: При критических ошибках загрузки
    """
    try:
        # Подготавливаем данные
        if isinstance(image_data, BytesIO):
            image_bytes = image_data.getvalue()
        else:
            image_bytes = image_data
            
        # Формируем multipart данные для загрузки
        data = aiohttp.FormData()
        data.add_field('files[]', 
                      image_bytes, 
                      filename='image.png', 
                      content_type='image/png')
        
        timeout = aiohttp.ClientTimeout(total=30)  # 30 секунд таймаут
        
        async with aiohttp.ClientSession(timeout=timeout) as session:
            async with session.post('https://uguu.se/upload', data=data) as response:
                if response.status == 200:
                    result = await response.json()
                    
                    # uguu.se возвращает массив с информацией о файлах
                    if result.get('success') and result.get('files'):
                        file_url = result['files'][0].get('url')
                        if file_url:
                            logger.info(f"Изображение успешно загружено на uguu.se: {file_url}")
                            return file_url
                    
                    logger.error(f"Неожиданный формат ответа от uguu.se: {result}")
                    return None
                else:
                    logger.error(f"Ошибка загрузки на uguu.se: HTTP {response.status}")
                    return None
                    
    except asyncio.TimeoutError:
        logger.error("Таймаут при загрузке изображения на uguu.se")
        return None
    except aiohttp.ClientError as e:
        logger.error(f"Сетевая ошибка при загрузке на uguu.se: {e}")
        return None
    except Exception as e:
        logger.exception(f"Неожиданная ошибка при загрузке на uguu.se: {e}")
        raise ImageUploadError(f"Критическая ошибка загрузки: {e}") from e


async def upload_to_0x0(image_data: Union[bytes, BytesIO]) -> Optional[str]:
    """
    Альтернативная загрузка на 0x0.st (резервный хостинг).
    
    Args:
        image_data: Данные изображения в виде bytes или BytesIO
        
    Returns:
        Публичная ссылка на загруженное изображение или None в случае ошибки
    """
    try:
        # Подготавливаем данные
        if isinstance(image_data, BytesIO):
            image_bytes = image_data.getvalue()
        else:
            image_bytes = image_data
            
        data = aiohttp.FormData()
        data.add_field('file', 
                      image_bytes, 
                      filename='image.png', 
                      content_type='image/png')
        
        timeout = aiohttp.ClientTimeout(total=30)
        
        async with aiohttp.ClientSession(timeout=timeout) as session:
            async with session.post('https://0x0.st', data=data) as response:
                if response.status == 200:
                    file_url = (await response.text()).strip()
                    if file_url.startswith('https://'):
                        logger.info(f"Изображение успешно загружено на 0x0.st: {file_url}")
                        return file_url
                    
                logger.error(f"Неожиданный ответ от 0x0.st: {await response.text()}")
                return None
                
    except Exception as e:
        logger.error(f"Ошибка загрузки на 0x0.st: {e}")
        return None


async def upload_to_public_host(image_data: Union[bytes, BytesIO], 
                               max_retries: int = 2) -> Optional[str]:
    """
    Основная функция загрузки изображения на публичный хостинг.
    
    Пытается загрузить изображение на несколько хостингов с повторными попытками.
    
    Args:
        image_data: Данные изображения в виде bytes или BytesIO
        max_retries: Максимальное количество попыток для каждого хостинга
        
    Returns:
        Публичная ссылка на загруженное изображение или None если все попытки неудачны
        
    Raises:
        ImageUploadError: При критических ошибках
    """
    # Список функций загрузки в порядке приоритета
    upload_functions = [upload_to_uguu, upload_to_0x0]
    
    for upload_func in upload_functions:
        for attempt in range(max_retries):
            try:
                logger.debug(f"Попытка загрузки через {upload_func.__name__}, попытка {attempt + 1}")
                
                result = await upload_func(image_data)
                if result:
                    return result
                    
                # Небольшая задержка между попытками
                if attempt < max_retries - 1:
                    await asyncio.sleep(1)
                    
            except ImageUploadError:
                # Критические ошибки пробрасываем дальше
                raise
            except Exception as e:
                logger.warning(f"Ошибка в {upload_func.__name__}, попытка {attempt + 1}: {e}")
                if attempt < max_retries - 1:
                    await asyncio.sleep(1)
    
    logger.error("Все попытки загрузки изображения на публичные хостинги неудачны")
    return None


async def validate_image_url(url: str) -> bool:
    """
    Проверяет доступность загруженного изображения по URL.
    
    Args:
        url: URL для проверки
        
    Returns:
        True если изображение доступно, False иначе
    """
    try:
        timeout = aiohttp.ClientTimeout(total=10)
        
        async with aiohttp.ClientSession(timeout=timeout) as session:
            async with session.head(url) as response:
                return response.status == 200
                
    except Exception as e:
        logger.warning(f"Ошибка проверки URL {url}: {e}")
        return False
